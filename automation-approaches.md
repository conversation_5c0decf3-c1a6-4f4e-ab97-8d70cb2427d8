# AI Test Case Generator - Automation Approaches

## **Approach 1: Fully Automatic Flow**

### **Architecture Flow:**
```
AI Generator → TestRail API → Webhook Trigger → REST Assured → Results Update
```

### **Implementation Steps:**

#### **1. AI Generator Integration with TestRail**
```java
@Service
public class TestRailIntegrationService {
    
    @Autowired
    private TestRailClient testRailClient;
    
    @Autowired
    private AutomationTriggerService automationTrigger;
    
    public void processGeneratedTestCases(List<AIGeneratedTestCase> testCases, boolean autoExecute) {
        // Step 1: Create Test Run in TestRail
        TestRun testRun = testRailClient.createTestRun(
            "AI Generated Test Run - " + LocalDateTime.now(),
            "Automatically generated test cases from AI"
        );
        
        // Step 2: Import test cases to TestRail
        List<Integer> testCaseIds = new ArrayList<>();
        for (AIGeneratedTestCase aiTestCase : testCases) {
            TestCase testRailCase = convertToTestRailFormat(aiTestCase);
            int testCaseId = testRailClient.addTestCase(testRailCase);
            testCaseIds.add(testCaseId);
        }
        
        // Step 3: Add test cases to test run
        testRailClient.addTestCasesToRun(testRun.getId(), testCaseIds);
        
        // Step 4: Auto-trigger execution if enabled
        if (autoExecute) {
            automationTrigger.triggerExecution(testRun.getId(), testCaseIds);
        }
    }
    
    private TestCase convertToTestRailFormat(AIGeneratedTestCase aiTestCase) {
        return TestCase.builder()
            .title(aiTestCase.getScenario())
            .customSteps(Arrays.asList(
                new TestStep(aiTestCase.getGiven()),
                new TestStep(aiTestCase.getWhen()),
                new TestStep(aiTestCase.getThen())
            ))
            .customApiEndpoint(aiTestCase.getApiEndpoint())
            .customHttpMethod(aiTestCase.getHttpMethod())
            .customRequestBody(aiTestCase.getRequestBody())
            .customExpectedResponse(aiTestCase.getExpectedResponse())
            .build();
    }
}
```

#### **2. Automatic Execution Trigger**
```java
@Service
public class AutomationTriggerService {
    
    @Autowired
    private RestAssuredExecutor restAssuredExecutor;
    
    @Autowired
    private TestRailClient testRailClient;
    
    @Async
    public void triggerExecution(int testRunId, List<Integer> testCaseIds) {
        try {
            // Get test cases from TestRail
            List<TestCase> testCases = testRailClient.getTestCases(testCaseIds);
            
            // Execute REST Assured tests
            List<TestResult> results = restAssuredExecutor.executeTests(testCases);
            
            // Update results back to TestRail
            updateTestRailResults(testRunId, results);
            
            // Send notification
            notificationService.sendExecutionComplete(testRunId, results);
            
        } catch (Exception e) {
            log.error("Execution failed for test run: " + testRunId, e);
            notificationService.sendExecutionFailed(testRunId, e.getMessage());
        }
    }
    
    private void updateTestRailResults(int testRunId, List<TestResult> results) {
        for (TestResult result : results) {
            testRailClient.addResult(
                result.getTestCaseId(),
                result.isSuccess() ? TestRailStatus.PASSED.getId() : TestRailStatus.FAILED.getId(),
                result.getComment(),
                result.getElapsedTime()
            );
        }
    }
}
```

#### **3. REST Assured Executor**
```java
@Component
public class RestAssuredExecutor {
    
    public List<TestResult> executeTests(List<TestCase> testCases) {
        List<TestResult> results = new ArrayList<>();
        
        for (TestCase testCase : testCases) {
            TestResult result = executeTestCase(testCase);
            results.add(result);
        }
        
        return results;
    }
    
    private TestResult executeTestCase(TestCase testCase) {
        long startTime = System.currentTimeMillis();
        
        try {
            Response response = given()
                .contentType("application/json")
                .body(testCase.getCustomRequestBody())
            .when()
                .request(testCase.getCustomHttpMethod(), testCase.getCustomApiEndpoint())
            .then()
                .extract().response();
            
            // Validate response
            boolean isSuccess = validateResponse(response, testCase);
            long elapsedTime = System.currentTimeMillis() - startTime;
            
            return TestResult.builder()
                .testCaseId(testCase.getId())
                .success(isSuccess)
                .comment(isSuccess ? "Test passed successfully" : "Test failed: " + getFailureReason(response, testCase))
                .elapsedTime(elapsedTime)
                .build();
                
        } catch (Exception e) {
            long elapsedTime = System.currentTimeMillis() - startTime;
            return TestResult.builder()
                .testCaseId(testCase.getId())
                .success(false)
                .comment("Test failed with exception: " + e.getMessage())
                .elapsedTime(elapsedTime)
                .build();
        }
    }
    
    private boolean validateResponse(Response response, TestCase testCase) {
        // Parse expected response from test case
        JsonPath expectedJson = JsonPath.from(testCase.getCustomExpectedResponse());
        JsonPath actualJson = response.jsonPath();
        
        // Validate status code
        if (response.getStatusCode() != expectedJson.getInt("statusCode")) {
            return false;
        }
        
        // Validate response body structure
        // Add your validation logic here
        
        return true;
    }
}
```

---

## **Approach 2: Manual Review Flow**

### **Architecture Flow:**
```
AI Generator → TestRail API → Manual Review → Manual Trigger → REST Assured → Results Update
```

### **Implementation Steps:**

#### **1. Manual Review Dashboard Integration**
```java
@RestController
@RequestMapping("/api/manual-execution")
public class ManualExecutionController {
    
    @Autowired
    private TestRailIntegrationService testRailService;
    
    @Autowired
    private AutomationTriggerService automationTrigger;
    
    @PostMapping("/import-for-review")
    public ResponseEntity<ManualReviewResponse> importForReview(@RequestBody List<AIGeneratedTestCase> testCases) {
        // Import to TestRail without auto-execution
        TestRun testRun = testRailService.processGeneratedTestCases(testCases, false);
        
        ManualReviewResponse response = ManualReviewResponse.builder()
            .testRunId(testRun.getId())
            .testRunUrl("https://yourcompany.testrail.io/index.php?/runs/view/" + testRun.getId())
            .totalTestCases(testCases.size())
            .status("READY_FOR_REVIEW")
            .message("Test cases imported successfully. Please review in TestRail before execution.")
            .build();
            
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/trigger-execution")
    public ResponseEntity<ExecutionResponse> triggerExecution(@RequestBody ExecutionRequest request) {
        // Validate user has reviewed the test cases
        if (!validateReviewComplete(request.getTestRunId())) {
            return ResponseEntity.badRequest()
                .body(ExecutionResponse.error("Please complete review in TestRail before execution"));
        }
        
        // Get approved test cases
        List<Integer> approvedTestCases = testRailService.getApprovedTestCases(request.getTestRunId());
        
        // Trigger execution
        automationTrigger.triggerExecution(request.getTestRunId(), approvedTestCases);
        
        ExecutionResponse response = ExecutionResponse.builder()
            .testRunId(request.getTestRunId())
            .status("EXECUTION_STARTED")
            .message("Test execution started for " + approvedTestCases.size() + " approved test cases")
            .build();
            
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/schedule-execution")
    public ResponseEntity<ScheduleResponse> scheduleExecution(@RequestBody ScheduleRequest request) {
        // Schedule execution for later
        schedulerService.scheduleExecution(
            request.getTestRunId(),
            request.getScheduledTime(),
            request.getNotificationEmails()
        );
        
        return ResponseEntity.ok(ScheduleResponse.success("Execution scheduled successfully"));
    }
    
    private boolean validateReviewComplete(int testRunId) {
        // Check if all test cases in the run have been reviewed
        List<TestCase> testCases = testRailService.getTestCasesInRun(testRunId);
        return testCases.stream().allMatch(tc -> tc.getCustomReviewStatus() != null);
    }
}
```

#### **2. TestRail Custom Fields for Review**
```java
// Add custom fields to TestRail for review process
public class TestRailCustomFields {
    public static final String REVIEW_STATUS = "custom_review_status";
    public static final String REVIEWER_COMMENTS = "custom_reviewer_comments";
    public static final String APPROVED_FOR_EXECUTION = "custom_approved_for_execution";
    public static final String AI_GENERATED = "custom_ai_generated";
    
    public enum ReviewStatus {
        PENDING_REVIEW(1),
        APPROVED(2),
        NEEDS_MODIFICATION(3),
        REJECTED(4);
        
        private final int id;
        
        ReviewStatus(int id) {
            this.id = id;
        }
        
        public int getId() {
            return id;
        }
    }
}
```

#### **3. Notification Service**
```java
@Service
public class NotificationService {
    
    @Autowired
    private EmailService emailService;
    
    public void sendReviewNotification(int testRunId, List<String> reviewerEmails) {
        String subject = "AI Generated Test Cases Ready for Review - TR-" + testRunId;
        String body = buildReviewNotificationBody(testRunId);
        
        emailService.sendEmail(reviewerEmails, subject, body);
    }
    
    public void sendExecutionComplete(int testRunId, List<TestResult> results) {
        String subject = "Test Execution Completed - TR-" + testRunId;
        String body = buildExecutionSummary(results);
        
        List<String> stakeholders = getStakeholderEmails(testRunId);
        emailService.sendEmail(stakeholders, subject, body);
    }
    
    public void sendExecutionFailed(int testRunId, String errorMessage) {
        String subject = "Test Execution Failed - TR-" + testRunId;
        String body = "Test execution failed with error: " + errorMessage;
        
        List<String> admins = getAdminEmails();
        emailService.sendEmail(admins, subject, body);
    }
}
```

---

## **Configuration & Setup**

### **Application Properties:**
```yaml
# TestRail Configuration
testrail:
  url: https://yourcompany.testrail.io
  username: ${TESTRAIL_USERNAME}
  api-key: ${TESTRAIL_API_KEY}
  project-id: 1
  suite-id: 2

# Automation Configuration
automation:
  execution-timeout: 300000  # 5 minutes
  max-concurrent-tests: 10
  retry-failed-tests: true
  retry-count: 2

# Notification Configuration
notification:
  email:
    enabled: true
    smtp-host: smtp.company.com
    smtp-port: 587
  slack:
    enabled: true
    webhook-url: ${SLACK_WEBHOOK_URL}
```

### **Database Schema for Tracking:**
```sql
CREATE TABLE test_execution_tracking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    test_run_id INT NOT NULL,
    execution_mode ENUM('AUTOMATIC', 'MANUAL') NOT NULL,
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED') NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    total_tests INT,
    passed_tests INT,
    failed_tests INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

This implementation provides both fully automatic and manual review workflows, giving users flexibility in how they want to handle AI-generated test cases.
