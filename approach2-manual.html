<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approach 2: Manual Review Test Case Generation & Execution</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .flow-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 150px;
            position: relative;
        }

        .flow-step.manual {
            border: 2px solid #ff6b6b;
            background: #fff5f5;
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #ff6b6b;
            font-weight: bold;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .flow-step i {
            font-size: 32px;
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .flow-step h3 {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .flow-step p {
            color: #6c757d;
            font-size: 12px;
        }

        .benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .benefit-card {
            background: #fff5f5;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #ff6b6b;
        }

        .benefit-card h3 {
            color: #c62828;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .benefit-card p {
            color: #c62828;
            font-size: 14px;
        }

        .mvp-timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }

        .mvp-phase {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #ff6b6b;
            position: relative;
        }

        .mvp-phase::before {
            content: attr(data-phase);
            position: absolute;
            left: -15px;
            top: -10px;
            background: #ff6b6b;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .mvp-phase h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li::before {
            content: '✓';
            color: #ff6b6b;
            font-weight: bold;
        }

        .review-process {
            background: #fff5f5;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #ff6b6b;
        }

        .review-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .review-step {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .review-step i {
            font-size: 24px;
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .review-step h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .review-step p {
            color: #6c757d;
            font-size: 12px;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tech-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .tech-item i {
            font-size: 32px;
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .tech-item h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .tech-item p {
            color: #6c757d;
            font-size: 12px;
        }

        .cta-section {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px;
            margin-top: 40px;
        }

        .btn {
            background: white;
            color: #ff6b6b;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .comparison-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            text-align: left;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .flow-steps {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
            
            .benefits {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-user-check"></i>
                Approach 2: Manual Review Flow
            </h1>
            <p>Quality-First Approach with Human Oversight & Control</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Overview Section -->
            <div class="section">
                <h2><i class="fas fa-info-circle"></i> Overview</h2>
                <p style="font-size: 16px; line-height: 1.6; color: #495057;">
                    The Manual Review approach combines AI efficiency with human expertise. Test cases are generated by AI 
                    and imported to TestRail, but require human review and approval before execution. This ensures quality control, 
                    compliance with regulations, and allows teams to learn from and improve AI-generated test patterns.
                </p>
            </div>

            <!-- Flow Diagram -->
            <div class="section">
                <h2><i class="fas fa-sitemap"></i> Process Flow</h2>
                <div class="flow-diagram">
                    <div class="flow-steps">
                        <div class="flow-step">
                            <i class="fas fa-upload"></i>
                            <h3>Upload & Configure</h3>
                            <p>User uploads documents and selects manual review mode</p>
                        </div>
                        <div class="flow-step">
                            <i class="fas fa-robot"></i>
                            <h3>AI Generation</h3>
                            <p>AI generates BDD test cases from documents</p>
                        </div>
                        <div class="flow-step">
                            <i class="fas fa-project-diagram"></i>
                            <h3>TestRail Import</h3>
                            <p>Test cases imported to TestRail for review</p>
                        </div>
                        <div class="flow-step manual">
                            <i class="fas fa-user-check"></i>
                            <h3>Manual Review</h3>
                            <p>Human review, approve, modify, or reject test cases</p>
                        </div>
                        <div class="flow-step">
                            <i class="fas fa-play"></i>
                            <h3>Triggered Execution</h3>
                            <p>Manual or scheduled execution of approved tests</p>
                        </div>
                        <div class="flow-step">
                            <i class="fas fa-chart-line"></i>
                            <h3>Results & Reports</h3>
                            <p>Execution results and comprehensive reporting</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Review Process -->
            <div class="section">
                <h2><i class="fas fa-clipboard-check"></i> Review Process Details</h2>
                <div class="review-process">
                    <h3 style="color: #c62828; margin-bottom: 15px;">TestRail Review Workflow</h3>
                    <div class="review-steps">
                        <div class="review-step">
                            <i class="fas fa-eye"></i>
                            <h4>Review Test Cases</h4>
                            <p>Examine AI-generated test scenarios in TestRail dashboard</p>
                        </div>
                        <div class="review-step">
                            <i class="fas fa-edit"></i>
                            <h4>Modify if Needed</h4>
                            <p>Edit test steps, expected results, or test data</p>
                        </div>
                        <div class="review-step">
                            <i class="fas fa-check-circle"></i>
                            <h4>Approve/Reject</h4>
                            <p>Mark test cases as approved or rejected for execution</p>
                        </div>
                        <div class="review-step">
                            <i class="fas fa-comments"></i>
                            <h4>Add Comments</h4>
                            <p>Provide feedback and improvement suggestions</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Benefits -->
            <div class="section">
                <h2><i class="fas fa-star"></i> Key Benefits</h2>
                <div class="benefits">
                    <div class="benefit-card">
                        <h3><i class="fas fa-shield-alt"></i> Quality Assurance</h3>
                        <p>Human oversight ensures test case accuracy and relevance. Catch edge cases AI might miss.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-balance-scale"></i> Compliance Ready</h3>
                        <p>Meets regulatory requirements for manual review and approval processes in critical systems.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-graduation-cap"></i> Team Learning</h3>
                        <p>Teams learn from AI patterns and improve their testing strategies over time.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-sliders-h"></i> Flexibility</h3>
                        <p>Full control over when and which tests to execute. Adapt to changing priorities.</p>
                    </div>
                </div>
            </div>

            <!-- MVP Timeline -->
            <div class="section">
                <h2><i class="fas fa-calendar-alt"></i> MVP Development Plan</h2>
                <div class="mvp-timeline">
                    <div class="mvp-phase" data-phase="MVP 1">
                        <h3>Basic Review Workflow (Month 1)</h3>
                        <ul class="feature-list">
                            <li>AI test case generation with TestRail import</li>
                            <li>Basic review interface in TestRail</li>
                            <li>Manual execution trigger system</li>
                            <li>Simple approval/rejection workflow</li>
                            <li>Email notifications for reviewers</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Review and execute 20+ test cases manually</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 2">
                        <h3>Enhanced Review Features (Month 2)</h3>
                        <ul class="feature-list">
                            <li>Advanced TestRail custom fields for review status</li>
                            <li>Bulk approval/rejection capabilities</li>
                            <li>Test case modification tracking</li>
                            <li>Reviewer assignment and notifications</li>
                            <li>Scheduled execution options</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Handle 50+ test cases with multiple reviewers</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 3">
                        <h3>Advanced Review Analytics (Month 3)</h3>
                        <ul class="feature-list">
                            <li>Review metrics and analytics dashboard</li>
                            <li>AI improvement feedback loop</li>
                            <li>Test case quality scoring</li>
                            <li>Review time tracking and optimization</li>
                            <li>Integration with project management tools</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Comprehensive review analytics for 100+ test cases</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 4">
                        <h3>Enterprise Review Management (Month 4)</h3>
                        <ul class="feature-list">
                            <li>Multi-level approval workflows</li>
                            <li>Role-based review permissions</li>
                            <li>Compliance reporting and audit trails</li>
                            <li>Advanced scheduling and resource management</li>
                            <li>Integration with enterprise tools (JIRA, Slack)</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Enterprise-ready review system across all teams</p>
                    </div>
                </div>
            </div>

            <!-- Comparison Table -->
            <div class="section">
                <h2><i class="fas fa-balance-scale"></i> Manual vs Automatic Comparison</h2>
                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Aspect</th>
                                <th>Manual Review Approach</th>
                                <th>Fully Automatic Approach</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Speed</strong></td>
                                <td>Slower due to review process</td>
                                <td>Fastest - immediate execution</td>
                            </tr>
                            <tr>
                                <td><strong>Quality Control</strong></td>
                                <td>High - human oversight</td>
                                <td>Medium - AI-dependent</td>
                            </tr>
                            <tr>
                                <td><strong>Compliance</strong></td>
                                <td>Excellent - audit trails</td>
                                <td>Good - automated logging</td>
                            </tr>
                            <tr>
                                <td><strong>Resource Requirements</strong></td>
                                <td>Higher - requires reviewers</td>
                                <td>Lower - minimal human input</td>
                            </tr>
                            <tr>
                                <td><strong>Flexibility</strong></td>
                                <td>High - full control</td>
                                <td>Medium - configuration-based</td>
                            </tr>
                            <tr>
                                <td><strong>Learning Opportunity</strong></td>
                                <td>High - team learns from AI</td>
                                <td>Low - black box execution</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Technology Stack -->
            <div class="section">
                <h2><i class="fas fa-cogs"></i> Technology Stack</h2>
                <div class="tech-stack">
                    <div class="tech-item">
                        <i class="fas fa-robot"></i>
                        <h4>AI Engine</h4>
                        <p>Local LLM with feedback learning capabilities</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-project-diagram"></i>
                        <h4>TestRail API</h4>
                        <p>Enhanced with custom review fields</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-users"></i>
                        <h4>Review System</h4>
                        <p>Multi-user review and approval workflow</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-clock"></i>
                        <h4>Scheduler</h4>
                        <p>Flexible execution scheduling system</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-chart-bar"></i>
                        <h4>Analytics</h4>
                        <p>Review metrics and quality tracking</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-bell"></i>
                        <h4>Notifications</h4>
                        <p>Multi-channel notification system</p>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <h2>Ready for Quality-Controlled AI Testing?</h2>
                <p>Combine AI efficiency with human expertise for the highest quality test cases</p>
                <button class="btn" onclick="window.open('approach1-automatic.html', '_blank')">
                    <i class="fas fa-eye"></i> Compare with Fully Automatic Approach
                </button>
                <button class="btn" onclick="window.open('index.html', '_blank')">
                    <i class="fas fa-desktop"></i> View Live Dashboard Demo
                </button>
            </div>
        </div>
    </div>
</body>
</html>
