Imagine you are an experienced Software Engineer helping me write high-quality
Playwright test scripts in TypeScript based on the test cases I provided.
Please go over the task twice to make sure the scripts are accurate and reliable.
Avoid making things up and do no hallucinate.
Use all the extra information outlined below, to write the best possible scripts, tailored for my project.

        # Project Context

Look at the "Project_name" folder, to get more insights
 (if your project is quite large, use the below section to be more concrete and reference specific folders/files).

My project structure includes:

        *   Authentication helpers: //*Add/folder/path*

        *   Existing sample tests: //*Add/folder/path*

        *   Playwright config: //*Add/folder/path*

        *   Test Structure: //*Add/folder/path/test-1656280.spec.ts*

        *   The project’s UX components are in the following folder: //*Add/folder/path*.

        # Test Structure Requirements

For each test, please follow this structure:

1.  Clear test description using *'test.describe()'* blocks

2.  Proper authentication setup before any page navigation

3.  Robust selector strategies with multiple fallbacks

4.  Detailed logging for debugging

5.  Screenshot captures at key points for verification

6.  Proper error handling with clear error messages

7.  Appropriate timeouts and wait strategies

8.  Verification/assertion steps that match the test case acceptance criteria

# Robustness Requirements

Each test should include:

1.  Retry mechanisms for flaky UI elements

2.  Multiple selector strategies to find elements

3.  Explicit waits for network idle and page load states

4.  Clear logging of each test step

5.  Detailed error reporting and screenshots on failure

6.  Handling of unexpected dialogs or notifications

7.  Timeout handling with clear error messages

# Environmental Considerations

The tests will run in:

*   CI/CD pipeline environments

*   Headless mode by default

*   Potentially with network latency

*   Different viewport sizes

# Example Usage

Please provide a complete implementation with:

1.  Helper functions for authentication and common operations

2.  Full test implementation for each test case

        3.  Comments explaining complex logic

4.  Guidance on test execution

# Authentication Approach

In order for the tests to be executed, we need to authenticate the application. Use the below auth approach:

//{you need to define the authentication steps – if this is already defined for your project,
 instruct Copilot how to use it. If your scenarios do not require auth, you can remove this part from the prompt.}

        # Configuration Reference

        For timeouts, screenshot settings, and other configuration options, please refer to:

//{Add a reference to a specific file, etc. for better context}

I want these tests to be maintainable, reliable, and provide clear feedback when they fail.