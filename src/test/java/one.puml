@startuml AI_Test_Generator_Authentication
!theme plain
title AI Test Case Generator - Authentication & Authorization Flow

actor "QA User" as User
participant "React Frontend" as Frontend
participant "API Gateway" as Gateway
participant "Auth Service" as AuthService
participant "LDAP/AD Server" as LDAP
participant "JWT Service" as JWT
participant "User Service" as UserService
database "User Database" as UserDB
participant "Protected Resource" as Resource

== Initial Login ==
User -> Frontend: access application
Frontend -> Frontend: check local storage for token

alt No Token Found
    Frontend -> User: redirect to login page
    User -> Frontend: enter credentials (username/password)

    Frontend -> Gateway: POST /api/auth/login
    Gateway -> AuthService: authenticate(credentials)

    AuthService -> LDAP: validate credentials
    LDAP -> AuthService: user authenticated + user details

    AuthService -> UserService: getUserRoles(username)
    UserService -> UserDB: SELECT roles FROM users
    UserDB -> UserService: user roles
    UserService -> AuthService: roles returned

    AuthService -> JWT: generateToken(user, roles)
    JWT -> AuthService: JWT token

    AuthService -> Gateway: authentication successful + token
    Gateway -> Frontend: 200 OK + JWT token

    Frontend -> Frontend: store token in secure storage
    Frontend -> User: redirect to dashboard

else Token Exists
    Frontend -> Gateway: GET /api/auth/validate
    Gateway -> JWT: validateToken(token)

    alt Token Valid
        JWT -> Gateway: token valid + user info
        Gateway -> Frontend: user authenticated
        Frontend -> User: show dashboard
    else Token Invalid/Expired
        JWT -> Gateway: token invalid
        Gateway -> Frontend: 401 Unauthorized
        Frontend -> Frontend: clear stored token
        Frontend -> User: redirect to login
    end
end

== Accessing Protected Resources ==
User -> Frontend: request protected action
Frontend -> Gateway: API call with Authorization header
Gateway -> JWT: validateToken(bearerToken)

alt Token Valid
    JWT -> Gateway: token valid + user claims
    Gateway -> Gateway: check user permissions

    alt User Authorized
        Gateway -> Resource: forward request
        Resource -> Gateway: response data
        Gateway -> Frontend: 200 OK + data
        Frontend -> User: display result
    else User Not Authorized
        Gateway -> Frontend: 403 Forbidden
        Frontend -> User: "Access denied"
    end

else Token Invalid
    JWT -> Gateway: token invalid
    Gateway -> Frontend: 401 Unauthorized
    Frontend -> Frontend: clear token
    Frontend -> User: redirect to login
end

== Token Refresh ==
Frontend -> Frontend: check token expiry (before API calls)

alt Token Near Expiry
    Frontend -> Gateway: POST /api/auth/refresh
    Gateway -> JWT: refreshToken(currentToken)

    alt Refresh Successful
        JWT -> Gateway: new token
        Gateway -> Frontend: new JWT token
        Frontend -> Frontend: update stored token
        Frontend -> Gateway: continue with original request
    else Refresh Failed
        JWT -> Gateway: refresh failed
        Gateway -> Frontend: 401 Unauthorized
        Frontend -> User: redirect to login
    end
end

== Logout ==
User -> Frontend: click logout
Frontend -> Gateway: POST /api/auth/logout
Gateway -> JWT: invalidateToken(token)
JWT -> Gateway: token invalidated
Gateway -> Frontend: logout successful
Frontend -> Frontend: clear all stored tokens
Frontend -> User: redirect to login page

== Role-Based Access Control ==
note over Gateway, Resource
    **User Roles & Permissions:**
    • **QA_TESTER**: Upload docs, review tests, execute
    • **QA_LEAD**: All tester permissions + approve workflows
    • **ADMIN**: All permissions + user management
    • **VIEWER**: Read-only access to results
end note

@enduml