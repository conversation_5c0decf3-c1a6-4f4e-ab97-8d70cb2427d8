<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java Developer Resume</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            color: #333;
            background: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
            margin-bottom: 25px;
        }
        .name {
            font-size: 28px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 8px;
        }
        .title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .contact {
            font-size: 14px;
            color: #555;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c5aa0;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
            margin-bottom: 15px;
            text-transform: uppercase;
        }
        .job-title {
            font-weight: bold;
            font-size: 15px;
            color: #333;
        }
        .company {
            font-weight: bold;
            color: #2c5aa0;
        }
        .duration {
            color: #666;
            font-style: italic;
            float: right;
        }
        .location {
            color: #666;
            font-size: 14px;
        }
        .technologies {
            background: #f8f9fa;
            padding: 8px;
            border-left: 4px solid #2c5aa0;
            margin: 8px 0;
            font-size: 14px;
        }
        .achievements {
            margin: 10px 0;
        }
        .achievements li {
            margin-bottom: 6px;
            line-height: 1.3;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .skill-category {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 5px;
        }
        .skill-category h4 {
            color: #2c5aa0;
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        .skill-list {
            font-size: 13px;
            line-height: 1.4;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .container { box-shadow: none; padding: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="name">[CANDIDATE NAME]</div>
            <div class="title">Senior Java Developer | Microservices & Cloud Architecture Specialist</div>
            <div class="contact">
                📧 [<EMAIL>] | 📱 [+81-XXX-XXXX-XXXX] | 🌐 Tokyo, Japan | 💼 LinkedIn: [profile]
            </div>
        </div>

        <!-- Professional Summary -->
        <div class="section">
            <div class="section-title">Professional Summary</div>
            <p>
                <strong>Senior Java Developer</strong> with <span class="highlight">5+ years of hands-on experience</span> designing and implementing 
                <strong>enterprise-grade microservices</strong> and <strong>RESTful APIs</strong> for connected automotive platforms. 
                Proven expertise in <strong>Spring Boot ecosystem</strong>, cloud-native architectures, and leading cross-functional teams 
                through complex <strong>cloud migrations</strong>. Demonstrated success in <strong>performance optimization</strong> (80% improvement), 
                <strong>system reliability</strong> (15% improvement), and <strong>automated testing frameworks</strong> that reduced defects by 90%.
            </p>
        </div>

        <!-- Technical Expertise -->
        <div class="section">
            <div class="section-title">Core Technical Expertise</div>
            <div class="skills-grid">
                <div class="skill-category">
                    <h4>🔧 Java Development (4.5 years)</h4>
                    <div class="skill-list">
                        • Spring Boot, Spring Security, Spring Framework<br>
                        • RESTful API Design & Implementation<br>
                        • Microservices Architecture<br>
                        • JUnit, TestNG, Mockito
                    </div>
                </div>
                <div class="skill-category">
                    <h4>☁️ Cloud & DevOps</h4>
                    <div class="skill-list">
                        • AWS, Azure Kubernetes Service (AKS)<br>
                        • Docker, Kubernetes, CI/CD Pipelines<br>
                        • GitHub Actions, Infrastructure as Code<br>
                        • Redis Caching, RabbitMQ
                    </div>
                </div>
                <div class="skill-category">
                    <h4>🗄️ Database & Integration</h4>
                    <div class="skill-list">
                        • PostgreSQL, SQL Optimization<br>
                        • AMQP, MQTT Protocol<br>
                        • REST Assured, API Testing<br>
                        • System Integration
                    </div>
                </div>
                <div class="skill-category">
                    <h4>🛠️ Development Tools</h4>
                    <div class="skill-list">
                        • JIRA, Confluence, Git<br>
                        • Linux/Unix, Shell Scripting<br>
                        • Agile/Scrum Methodologies<br>
                        • Team Leadership & Mentoring
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Experience -->
        <div class="section">
            <div class="section-title">Professional Experience</div>
            
            <!-- Current Role -->
            <div style="margin-bottom: 25px;">
                <div class="job-title">Senior Java Developer & Technical Lead</div>
                <div class="company">Tata Consultancy Services</div>
                <div class="duration">Feb 2023 – Present</div>
                <div class="location">Client: Nissan Motors | Tokyo, Japan</div>
                <div class="technologies">
                    <strong>Tech Stack:</strong> Java, Spring Boot, REST APIs, TestNG, Docker, Kubernetes (AKS), GitHub Actions, Redis, PostgreSQL
                </div>
                <ul class="achievements">
                    <li><strong>Architected and developed</strong> microservices for connected car telemetry processing, handling real-time vehicle data streams</li>
                    <li><strong>Led technical migration</strong> of 6-member development team from Pivotal Cloud Foundry to Azure Kubernetes Service, ensuring zero downtime</li>
                    <li><strong>Designed RESTful APIs</strong> for vehicle services validation, improving system reliability by 15% through comprehensive testing frameworks</li>
                    <li><strong>Implemented performance optimizations</strong> using Redis caching strategies, achieving 80% performance improvement in microservice response times</li>
                    <li><strong>Built automated testing frameworks</strong> using TestNG and Mockito, reducing production defects by 90% and increasing test coverage by 40%</li>
                    <li><strong>Established CI/CD pipelines</strong> with GitHub Actions for continuous integration and automated deployment across multiple environments</li>
                    <li><strong>Mentored junior developers</strong> and collaborated with international teams across Japan, India, and offshore locations</li>
                </ul>
            </div>

            <!-- Previous Role -->
            <div style="margin-bottom: 25px;">
                <div class="job-title">Java Software Developer</div>
                <div class="company">Tata Consultancy Services</div>
                <div class="duration">Mar 2022 – Jan 2023</div>
                <div class="location">Pune, India</div>
                <div class="technologies">
                    <strong>Tech Stack:</strong> Java, Spring Boot, Redis, REST APIs, RabbitMQ, Docker, JUnit 5, PostgreSQL
                </div>
                <ul class="achievements">
                    <li><strong>Developed enterprise RESTful APIs</strong> for processing vehicle telemetry data with high throughput and low latency requirements</li>
                    <li><strong>Implemented microservice caching layer</strong> using Redis, resulting in 80% performance improvement and reduced database load</li>
                    <li><strong>Designed asynchronous communication</strong> using RabbitMQ and AMQP protocols for reliable message processing between services</li>
                    <li><strong>Containerized applications</strong> using Docker for consistent development and testing environments across teams</li>
                    <li><strong>Maintained comprehensive unit testing</strong> with JUnit 5, achieving over 90% code coverage and ensuring code quality</li>
                    <li><strong>Collaborated on API design</strong> and conducted thorough API testing using REST Assured and Postman</li>
                </ul>
            </div>

            <!-- Early Role -->
            <div style="margin-bottom: 25px;">
                <div class="job-title">Junior Java Developer</div>
                <div class="company">Tata Consultancy Services</div>
                <div class="duration">Jan 2021 – Feb 2022</div>
                <div class="location">Pune, India</div>
                <div class="technologies">
                    <strong>Tech Stack:</strong> Java, Spring Boot, Spring Security, React.js, Python, PyQt6, MQTT, JWT
                </div>
                <ul class="achievements">
                    <li><strong>Developed full-stack web application</strong> using React.js frontend and Spring Boot backend for test management system</li>
                    <li><strong>Implemented security features</strong> using Spring Security and JWT for role-based access control and authentication</li>
                    <li><strong>Built desktop application</strong> using PyQt6 for automation script management and execution</li>
                    <li><strong>Created real-time simulator</strong> using Python and MQTT protocol to replicate vehicle states for testing scenarios</li>
                    <li><strong>Designed mobile automation framework</strong> reducing manual testing efforts and improving development velocity</li>
                </ul>
            </div>
        </div>

        <!-- Key Projects & Achievements -->
        <div class="section">
            <div class="section-title">Key Technical Achievements</div>
            <ul class="achievements">
                <li><strong>🚀 Performance Optimization:</strong> Achieved 80% performance improvement through Redis-based microservice caching implementation</li>
                <li><strong>☁️ Cloud Migration Leadership:</strong> Successfully led team migration from PCF to Azure Kubernetes Service with zero downtime</li>
                <li><strong>🔧 System Reliability:</strong> Improved connected car services reliability by 15% through comprehensive testing frameworks</li>
                <li><strong>📈 Quality Improvement:</strong> Reduced production defects by 90% through automated testing and CI/CD implementation</li>
                <li><strong>👥 Team Leadership:</strong> Mentored 6-member cross-functional team including developers and QA engineers</li>
                <li><strong>🌐 Global Collaboration:</strong> Successfully worked with multicultural teams across Japan, India, and offshore locations</li>
            </ul>
        </div>

        <!-- Certifications & Recognition -->
        <div class="section">
            <div class="section-title">Professional Recognition & Awards</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>• <strong>Applause Award</strong> - Dec 2024</div>
                <div>• <strong>On the Spot Award</strong> - Sept 2024</div>
                <div>• <strong>Service & Commitment Award</strong> - Jan 2024</div>
                <div>• <strong>Star of the Month</strong> - Mar 2022</div>
            </div>
        </div>

        <!-- Languages -->
        <div class="section">
            <div class="section-title">Languages</div>
            <p><strong>English:</strong> Professional proficiency (Reading/Writing/Speaking) | <strong>Japanese:</strong> Conversational (Tokyo-based experience)</p>
        </div>
    </div>
</body>
</html>
