<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approach 1: Fully Automatic Test Case Generation & Execution</title>
    <!-- SVG Icons will be defined inline -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .flow-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 150px;
            position: relative;
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #28a745;
            font-weight: bold;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .flow-step i {
            font-size: 32px;
            color: #28a745;
            margin-bottom: 10px;
        }

        .flow-step h3 {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .flow-step p {
            color: #6c757d;
            font-size: 12px;
        }

        .benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .benefit-card {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #28a745;
        }

        .benefit-card h3 {
            color: #155724;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .benefit-card p {
            color: #155724;
            font-size: 14px;
        }

        .mvp-timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }

        .mvp-phase {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
            position: relative;
        }

        .mvp-phase::before {
            content: attr(data-phase);
            position: absolute;
            left: -15px;
            top: -10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .mvp-phase h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tech-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .tech-item i {
            font-size: 32px;
            color: #28a745;
            margin-bottom: 10px;
        }

        .tech-item h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .tech-item p {
            color: #6c757d;
            font-size: 12px;
        }

        .cta-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px;
            margin-top: 40px;
        }

        .btn {
            background: white;
            color: #28a745;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .flow-steps {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
            
            .benefits {
                grid-template-columns: 1fr;
            }
        }

        /* Icon styles */
        .icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            fill: currentColor;
            vertical-align: middle;
        }

        .icon-lg {
            width: 2em;
            height: 2em;
        }

        .icon-xl {
            width: 2.5em;
            height: 2.5em;
        }
    </style>
</head>
<body>
    <!-- SVG Icon Definitions -->
    <svg style="display: none;">
        <defs>
            <!-- Rocket Icon -->
            <symbol id="icon-rocket" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                <path d="M12 16L8 20H16L12 16Z"/>
            </symbol>

            <!-- Info Circle Icon -->
            <symbol id="icon-info" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="16" x2="12" y2="12"/>
                <line x1="12" y1="8" x2="12.01" y2="8"/>
            </symbol>

            <!-- Sitemap Icon -->
            <symbol id="icon-sitemap" viewBox="0 0 24 24">
                <rect x="3" y="3" width="6" height="6" rx="1"/>
                <rect x="15" y="3" width="6" height="6" rx="1"/>
                <rect x="9" y="15" width="6" height="6" rx="1"/>
                <path d="M6 9v3a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V9"/>
                <path d="M12 12v3"/>
            </symbol>

            <!-- Upload Icon -->
            <symbol id="icon-upload" viewBox="0 0 24 24">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,5 17,10"/>
                <line x1="12" y1="5" x2="12" y2="15"/>
            </symbol>

            <!-- Robot Icon -->
            <symbol id="icon-robot" viewBox="0 0 24 24">
                <rect x="6" y="8" width="12" height="12" rx="2"/>
                <path d="M12 2v6"/>
                <circle cx="9" cy="12" r="1"/>
                <circle cx="15" cy="12" r="1"/>
                <path d="M9 16h6"/>
            </symbol>

            <!-- Project Diagram Icon -->
            <symbol id="icon-project" viewBox="0 0 24 24">
                <rect x="2" y="3" width="6" height="6" rx="1"/>
                <rect x="16" y="3" width="6" height="6" rx="1"/>
                <rect x="9" y="15" width="6" height="6" rx="1"/>
                <path d="M5 9v3a3 3 0 0 0 3 3h4a3 3 0 0 0 3-3V9"/>
                <path d="M12 12v3"/>
            </symbol>

            <!-- Play Icon -->
            <symbol id="icon-play" viewBox="0 0 24 24">
                <polygon points="5,3 19,12 5,21"/>
            </symbol>

            <!-- Chart Line Icon -->
            <symbol id="icon-chart" viewBox="0 0 24 24">
                <polyline points="22,6 12,16 2,6"/>
                <path d="M16 6L22 6 22 12"/>
            </symbol>

            <!-- Star Icon -->
            <symbol id="icon-star" viewBox="0 0 24 24">
                <polygon points="12,2 15.09,8.26 22,9 17,14.74 18.18,21.02 12,17.77 5.82,21.02 7,14.74 2,9 8.91,8.26"/>
            </symbol>

            <!-- Clock Icon -->
            <symbol id="icon-clock" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
            </symbol>

            <!-- Sync Icon -->
            <symbol id="icon-sync" viewBox="0 0 24 24">
                <polyline points="23,4 23,10 17,10"/>
                <polyline points="1,20 1,14 7,14"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
            </symbol>

            <!-- Expand Icon -->
            <symbol id="icon-expand" viewBox="0 0 24 24">
                <polyline points="15,3 21,3 21,9"/>
                <polyline points="9,21 3,21 3,15"/>
                <line x1="21" y1="3" x2="14" y2="10"/>
                <line x1="3" y1="21" x2="10" y2="14"/>
            </symbol>

            <!-- Bell Icon -->
            <symbol id="icon-bell" viewBox="0 0 24 24">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </symbol>

            <!-- Calendar Icon -->
            <symbol id="icon-calendar" viewBox="0 0 24 24">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                <line x1="16" y1="2" x2="16" y2="6"/>
                <line x1="8" y1="2" x2="8" y2="6"/>
                <line x1="3" y1="10" x2="21" y2="10"/>
            </symbol>

            <!-- Cogs Icon -->
            <symbol id="icon-cogs" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3"/>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </symbol>

            <!-- Eye Icon -->
            <symbol id="icon-eye" viewBox="0 0 24 24">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
            </symbol>

            <!-- Desktop Icon -->
            <symbol id="icon-desktop" viewBox="0 0 24 24">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                <line x1="8" y1="21" x2="16" y2="21"/>
                <line x1="12" y1="17" x2="12" y2="21"/>
            </symbol>
        </defs>
    </svg>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <svg class="icon icon-xl"><use href="#icon-rocket"></use></svg>
                Approach 1: Fully Automatic Flow
            </h1>
            <p>Zero Manual Intervention - Complete End-to-End Automation</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Overview Section -->
            <div class="section">
                <h2><svg class="icon"><use href="#icon-info"></use></svg> Overview</h2>
                <p style="font-size: 16px; line-height: 1.6; color: #495057;">
                    The Fully Automatic approach provides complete end-to-end automation from test case generation to execution and reporting. 
                    Once documents are uploaded and configuration is set, the entire process runs without any manual intervention, 
                    making it perfect for continuous integration and high-volume testing scenarios.
                </p>
            </div>

            <!-- Flow Diagram -->
            <div class="section">
                <h2><svg class="icon"><use href="#icon-sitemap"></use></svg> Process Flow</h2>
                <div class="flow-diagram">
                    <div class="flow-steps">
                        <div class="flow-step">
                            <svg class="icon icon-lg"><use href="#icon-upload"></use></svg>
                            <h3>Upload & Configure</h3>
                            <p>User uploads documents and selects automatic mode</p>
                        </div>
                        <div class="flow-step">
                            <svg class="icon icon-lg"><use href="#icon-robot"></use></svg>
                            <h3>AI Generation</h3>
                            <p>AI generates BDD test cases from documents</p>
                        </div>
                        <div class="flow-step">
                            <svg class="icon icon-lg"><use href="#icon-project"></use></svg>
                            <h3>TestRail Import</h3>
                            <p>Test cases automatically imported to TestRail</p>
                        </div>
                        <div class="flow-step">
                            <svg class="icon icon-lg"><use href="#icon-play"></use></svg>
                            <h3>Auto Execution</h3>
                            <p>REST Assured tests triggered automatically</p>
                        </div>
                        <div class="flow-step">
                            <svg class="icon icon-lg"><use href="#icon-chart"></use></svg>
                            <h3>Results & Reports</h3>
                            <p>Automatic result updates and notifications</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Benefits -->
            <div class="section">
                <h2><i class="fas fa-star"></i> Key Benefits</h2>
                <div class="benefits">
                    <div class="benefit-card">
                        <h3><i class="fas fa-clock"></i> Time Efficiency</h3>
                        <p>Reduces test case creation and execution time by 90%. Complete automation eliminates manual bottlenecks.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-sync"></i> CI/CD Ready</h3>
                        <p>Perfect for continuous integration pipelines. Seamlessly integrates with existing DevOps workflows.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-expand-arrows-alt"></i> Scalability</h3>
                        <p>Handle hundreds of test cases across 60+ microservices without manual intervention.</p>
                    </div>
                    <div class="benefit-card">
                        <h3><i class="fas fa-bell"></i> Instant Feedback</h3>
                        <p>Immediate test results and notifications. Faster bug detection and resolution cycles.</p>
                    </div>
                </div>
            </div>

            <!-- MVP Timeline -->
            <div class="section">
                <h2><i class="fas fa-calendar-alt"></i> MVP Development Plan</h2>
                <div class="mvp-timeline">
                    <div class="mvp-phase" data-phase="MVP 1">
                        <h3>Basic Automation Pipeline (Month 1)</h3>
                        <ul class="feature-list">
                            <li>AI test case generation from single document type</li>
                            <li>Basic TestRail API integration</li>
                            <li>Simple REST Assured execution engine</li>
                            <li>Automatic result updates to TestRail</li>
                            <li>Email notifications for completion</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Generate and execute 20+ test cases automatically</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 2">
                        <h3>Enhanced Processing & Multiple Formats (Month 2)</h3>
                        <ul class="feature-list">
                            <li>Support for multiple document formats (PDF, Word, Excel)</li>
                            <li>Advanced AI processing for complex scenarios</li>
                            <li>Webhook-based execution triggers</li>
                            <li>Real-time execution status dashboard</li>
                            <li>Basic error handling and retry mechanisms</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Process 50+ test cases from multiple document types</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 3">
                        <h3>Production Features & Monitoring (Month 3)</h3>
                        <ul class="feature-list">
                            <li>Comprehensive logging and audit trails</li>
                            <li>Performance monitoring and metrics</li>
                            <li>Parallel test execution capabilities</li>
                            <li>Advanced reporting and analytics</li>
                            <li>Integration with CI/CD pipelines</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Handle 100+ test cases with full monitoring</p>
                    </div>

                    <div class="mvp-phase" data-phase="MVP 4">
                        <h3>Enterprise Ready (Month 4)</h3>
                        <ul class="feature-list">
                            <li>Multi-user support with role-based access</li>
                            <li>Advanced security and compliance features</li>
                            <li>Custom test case templates and patterns</li>
                            <li>Integration with multiple TestRail projects</li>
                            <li>Comprehensive documentation and training</li>
                        </ul>
                        <p><strong>Success Criteria:</strong> Production deployment across all 60 microservices</p>
                    </div>
                </div>
            </div>

            <!-- Technology Stack -->
            <div class="section">
                <h2><i class="fas fa-cogs"></i> Technology Stack</h2>
                <div class="tech-stack">
                    <div class="tech-item">
                        <i class="fas fa-robot"></i>
                        <h4>AI Engine</h4>
                        <p>Local LLM (Llama 2/Code Llama) for secure processing</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-project-diagram"></i>
                        <h4>TestRail API</h4>
                        <p>Seamless integration for test management</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-code"></i>
                        <h4>REST Assured</h4>
                        <p>Robust API testing framework</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-server"></i>
                        <h4>Spring Boot</h4>
                        <p>Microservices architecture for scalability</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-database"></i>
                        <h4>PostgreSQL</h4>
                        <p>Secure data storage and audit logging</p>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-bell"></i>
                        <h4>Webhook System</h4>
                        <p>Real-time execution triggers</p>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <h2>Ready to Implement Fully Automatic Testing?</h2>
                <p>Transform your testing process with complete automation from generation to execution</p>
                <button class="btn" onclick="window.open('approach2-manual.html', '_blank')">
                    <i class="fas fa-eye"></i> Compare with Manual Review Approach
                </button>
                <button class="btn" onclick="window.open('index.html', '_blank')">
                    <i class="fas fa-desktop"></i> View Live Dashboard Demo
                </button>
            </div>
        </div>
    </div>
</body>
</html>
