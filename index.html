<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Case Generator - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px dashed #dee2e6;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .upload-area {
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 15px;
        }

        .upload-icon {
            font-size: 48px;
            color: #6c757d;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }

        .config-group {
            margin-bottom: 25px;
        }

        .config-group h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .checkbox-item:hover {
            background: #e3f2fd;
            border-color: #007bff;
        }

        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .results-section {
            grid-column: 1 / -1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
        }

        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #28a745;
            margin-bottom: 20px;
        }

        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            text-align: center;
        }

        .info-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .info-label {
            color: #6c757d;
            font-size: 14px;
        }

        .download-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .file-list {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #dee2e6;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 85%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-robot"></i>
                Dashboard
            </h1>
            <div class="user-info">
                <span><i class="fas fa-user"></i> Harsh Varshney (QA Member)</span>
                <button class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Document Upload Section -->
            <div class="upload-section">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">
                    <i class="fas fa-upload"></i> Document Upload
                </h2>
                <div class="upload-area">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <h3>Drag & Drop your documents here</h3>
                    <p style="color: #6c757d;">Supported formats: PDF, Word, Excel</p>
                    <p style="color: #6c757d;">Maximum size: 50MB per file</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-folder-open"></i> Browse Files
                    </button>
                </div>
                
                <!-- File List -->
                <div class="file-list" style="display: none;" id="fileList">
                    <div class="file-item">
                        <i class="fas fa-file-pdf" style="color: #dc3545;"></i>
                        <span>API_Specification_v2.1.pdf</span>
                        <span style="margin-left: auto; color: #28a745;">✓ Uploaded</span>
                    </div>
                    <div class="file-item">
                        <i class="fas fa-file-word" style="color: #007bff;"></i>
                        <span>HLD_Microservices_Architecture.docx</span>
                        <span style="margin-left: auto; color: #28a745;">✓ Uploaded</span>
                    </div>
                    <div class="file-item">
                        <i class="fas fa-file-excel" style="color: #28a745;"></i>
                        <span>Database_Schema_Design.xlsx</span>
                        <span style="margin-left: auto; color: #ffc107;">⏳ Processing</span>
                    </div>
                </div>
            </div>

            <!-- Configuration Section -->
            <div class="config-section">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">
                    <i class="fas fa-cogs"></i> Test Case Configuration
                </h2>

                <!-- Document Types -->
                <div class="config-group">
                    <h3><i class="fas fa-file-alt"></i> Document Types (Multi-select)</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" checked> LLD (Low Level Design)
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" checked> HLD (High Level Design)
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" checked> API Specification
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> Database Schema
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> Business Requirements
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> User Stories
                        </label>
                    </div>
                </div>

                <!-- Test Case Types -->
                <div class="config-group">
                    <h3><i class="fas fa-tasks"></i> Test Case Types (Multi-select)</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" checked> Functional Testing
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" checked> Integration Testing
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> Regression Testing
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> Performance Testing
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> Security Testing
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox"> API Testing
                        </label>
                    </div>
                </div>

                <!-- Output Format -->
                <div class="config-group">
                    <h3><i class="fas fa-download"></i> Output Format</h3>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="output" value="excel"> Excel File (.xlsx)
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="output" value="postman"> Postman Collection (.json)
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="output" value="testrail"> TestRail Import (.json)
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="output" value="all" checked> All Formats
                        </label>
                    </div>
                </div>

                <!-- TestRail Integration -->
                <div class="config-group">
                    <h3><i class="fas fa-project-diagram"></i> TestRail Integration</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="testrailIntegration" onchange="toggleTestRailOptions()"> Enable TestRail Integration
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="autoExecution" disabled> Auto-Execute After Import
                        </label>
                    </div>

                    <div id="testrailOptions" style="display: none; margin-top: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label style="font-size: 12px; color: #6c757d;">Project ID:</label>
                                <input type="text" placeholder="e.g., 1" style="width: 100%; padding: 5px; border: 1px solid #dee2e6; border-radius: 3px;">
                            </div>
                            <div>
                                <label style="font-size: 12px; color: #6c757d;">Suite ID:</label>
                                <input type="text" placeholder="e.g., 2" style="width: 100%; padding: 5px; border: 1px solid #dee2e6; border-radius: 3px;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Execution Mode -->
                <div class="config-group">
                    <h3><i class="fas fa-play-circle"></i> Execution Mode</h3>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="execution" value="automatic" checked>
                            <div>
                                <strong>Fully Automatic</strong>
                                <div style="font-size: 12px; color: #6c757d;">Generate → TestRail → Auto-Execute</div>
                            </div>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="execution" value="manual">
                            <div>
                                <strong>Manual Review</strong>
                                <div style="font-size: 12px; color: #6c757d;">Generate → TestRail → Review → Manual Execute</div>
                            </div>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="execution" value="generate-only">
                            <div>
                                <strong>Generate Only</strong>
                                <div style="font-size: 12px; color: #6c757d;">Generate test cases without execution</div>
                            </div>
                        </label>
                    </div>
                </div>

                <button class="btn btn-primary" style="width: 100%; margin-top: 20px;" onclick="showResults()">
                    <i class="fas fa-magic"></i> Generate & Process Test Cases
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="resultsSection" style="display: none;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">
                <i class="fas fa-chart-line"></i> Generated Results
            </h2>
            
            <div class="status-card">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 20px;"></i>
                    <h3 style="color: #28a745;">Processing Complete</h3>
                </div>
                
                <div class="status-info">
                    <div class="info-item">
                        <div class="info-value">127</div>
                        <div class="info-label">Test Cases Generated</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">3m 42s</div>
                        <div class="info-label">Processing Time</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">3</div>
                        <div class="info-label">Documents Processed</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">85%</div>
                        <div class="info-label">Coverage Score</div>
                    </div>
                </div>

                <!-- TestRail Integration Status -->
                <div id="testrailStatus" style="display: none; margin-top: 15px;">
                    <div style="background: #e3f2fd; border-radius: 8px; padding: 15px; border-left: 4px solid #2196f3;">
                        <h4 style="color: #1976d2; margin-bottom: 10px;">
                            <i class="fas fa-project-diagram"></i> TestRail Integration Status
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div class="info-item">
                                <div class="info-value" style="color: #1976d2;">✓ Imported</div>
                                <div class="info-label">TestRail Status</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" style="color: #1976d2;">TR-2024-001</div>
                                <div class="info-label">Test Run ID</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" style="color: #1976d2;">API Testing</div>
                                <div class="info-label">Test Suite</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Execution Status -->
                <div id="executionStatus" style="display: none; margin-top: 15px;">
                    <div style="background: #f3e5f5; border-radius: 8px; padding: 15px; border-left: 4px solid #9c27b0;">
                        <h4 style="color: #7b1fa2; margin-bottom: 10px;">
                            <i class="fas fa-play-circle"></i> Automation Execution Status
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div class="info-item">
                                <div class="info-value" style="color: #7b1fa2;">🚀 Running</div>
                                <div class="info-label">Execution Status</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" style="color: #7b1fa2;">45/127</div>
                                <div class="info-label">Tests Executed</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" style="color: #7b1fa2;">REST Assured</div>
                                <div class="info-label">Framework</div>
                            </div>
                        </div>
                        <div class="progress-bar" style="margin-top: 10px;">
                            <div class="progress-fill" style="width: 35%; background: linear-gradient(90deg, #9c27b0, #e91e63);"></div>
                        </div>
                    </div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <div class="download-buttons">
                <button class="btn btn-primary">
                    <i class="fas fa-file-excel"></i> Download Excel File
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-download"></i> Download Postman Collection
                </button>
                <button class="btn btn-primary" id="testrailBtn" style="display: none;">
                    <i class="fas fa-project-diagram"></i> View in TestRail
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-eye"></i> Preview Results
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-chart-bar"></i> Generate Report
                </button>
            </div>

            <!-- Manual Execution Controls (for Manual Review mode) -->
            <div id="manualExecutionControls" style="display: none; margin-top: 20px;">
                <div style="background: #fff3cd; border-radius: 8px; padding: 20px; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 15px;">
                        <i class="fas fa-hand-paper"></i> Manual Review & Execution
                    </h4>
                    <p style="color: #856404; margin-bottom: 15px;">
                        Test cases have been imported to TestRail. Please review them before execution.
                    </p>
                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <button class="btn btn-primary" onclick="openTestRail()">
                            <i class="fas fa-external-link-alt"></i> Review in TestRail
                        </button>
                        <button class="btn btn-success" onclick="triggerExecution()">
                            <i class="fas fa-play"></i> Execute Tests
                        </button>
                        <button class="btn btn-warning" onclick="scheduleExecution()">
                            <i class="fas fa-clock"></i> Schedule Execution
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResults() {
            // Show file list
            document.getElementById('fileList').style.display = 'block';

            // Show results section after a delay
            setTimeout(() => {
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
            }, 1000);
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Show file list on page load for demo
            setTimeout(() => {
                document.getElementById('fileList').style.display = 'block';
            }, 500);
        });
    </script>
</body>
</html>
